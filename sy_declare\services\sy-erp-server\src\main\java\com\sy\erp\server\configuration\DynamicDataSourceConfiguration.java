package com.sy.erp.server.configuration;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;

import javax.sql.DataSource;

@Configuration
public class DynamicDataSourceConfiguration {


    @Bean(name = "aimoProdMysql")
    @ConfigurationProperties(prefix = "spring.datasource.aimo.prod.mysql")
    public DataSource aimoProdMysql() {
        return new HikariDataSource();
    }

    @Bean(name = "aimoTestMysql")
    @ConfigurationProperties(prefix = "spring.datasource.aimo.test.mysql")
    public DataSource aimoTestMysql() {
        return new HikariDataSource();
    }

    @Bean(name = "a2019Sqlserver")
    @ConfigurationProperties(prefix = "spring.datasource.a2019.sqlserver")
    public DataSource a2019Sqlserver() {
        return new HikariDataSource();
    }

    @Bean(name = "xw2020Sqlserver")
    @ConfigurationProperties(prefix = "spring.datasource.xw2020.sqlserver")
    public DataSource xw2020Sqlserver() {
        return new HikariDataSource();
    }

    @Bean(name = "lpimsoftmesSqlserver")
    @ConfigurationProperties(prefix = "spring.datasource.lpimsoftmes.sqlserver")
    public DataSource lpimsoftmesSqlserver() {
        return new HikariDataSource();
    }

    @Bean(name = "dataSource")
    @DependsOn("aimoProdMysql")
    public DataSource dataSource() {
        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
        dataSource.setAimoProdMysqlDataSource(aimoProdMysql());
        dataSource.setAimoTestMysqlDataSource(aimoTestMysql());
        dataSource.setA2019SqlserverDataSource(a2019Sqlserver());
        dataSource.setXw2020SqlserverDataSource(xw2020Sqlserver());
        dataSource.setLpimsoftmesSqlserverDataSource(lpimsoftmesSqlserver());
        return dataSource;
    }
}
